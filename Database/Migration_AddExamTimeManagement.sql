-- 考试时间管理功能数据库迁移脚本
-- 执行日期：请在执行前备份数据库

USE [您的数据库名称]
GO

-- 添加考试时间管理字段到 ExamPapers 表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'StartTime')
BEGIN
    ALTER TABLE [dbo].[ExamPapers] ADD [StartTime] DATETIME2(7) NULL
    PRINT '已添加 StartTime 字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'EndTime')
BEGIN
    ALTER TABLE [dbo].[ExamPapers] ADD [EndTime] DATETIME2(7) NULL
    PRINT '已添加 EndTime 字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'DurationMinutes')
BEGIN
    ALTER TABLE [dbo].[ExamPapers] ADD [DurationMinutes] INT NULL
    PRINT '已添加 DurationMinutes 字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'EnableTimeLimit')
BEGIN
    ALTER TABLE [dbo].[ExamPapers] ADD [EnableTimeLimit] BIT NOT NULL DEFAULT 0
    PRINT '已添加 EnableTimeLimit 字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'AutoSubmitOnTimeout')
BEGIN
    ALTER TABLE [dbo].[ExamPapers] ADD [AutoSubmitOnTimeout] BIT NOT NULL DEFAULT 1
    PRINT '已添加 AutoSubmitOnTimeout 字段'
END

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[ExamPapers]') AND name = 'IX_ExamPapers_StartTime_EndTime')
BEGIN
    CREATE INDEX [IX_ExamPapers_StartTime_EndTime] ON [dbo].[ExamPapers] ([StartTime], [EndTime])
    PRINT '已创建时间查询索引'
END

-- 添加约束：结束时间必须大于开始时间
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_ExamPapers_TimeRange]'))
BEGIN
    ALTER TABLE [dbo].[ExamPapers] 
    ADD CONSTRAINT [CK_ExamPapers_TimeRange] 
    CHECK ([EndTime] IS NULL OR [StartTime] IS NULL OR [EndTime] > [StartTime])
    PRINT '已添加时间范围约束'
END

-- 添加约束：启用时间限制时必须设置持续时间或结束时间
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_ExamPapers_TimeLimit]'))
BEGIN
    ALTER TABLE [dbo].[ExamPapers] 
    ADD CONSTRAINT [CK_ExamPapers_TimeLimit] 
    CHECK ([EnableTimeLimit] = 0 OR [DurationMinutes] IS NOT NULL OR [EndTime] IS NOT NULL)
    PRINT '已添加时间限制约束'
END

PRINT '考试时间管理功能数据库迁移完成！'
