@using SuntechApp.Data
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <div class="d-flex align-center mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Preview" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5" Color="Color.Primary">试卷预览</MudText>
                <MudText Typo="Typo.body2" Color="Color.Info">@Paper?.Title</MudText>
            </div>
        </div>

        @if (loading)
        {
            <div class="d-flex justify-center align-center" style="height: 400px;">
                <MudProgressCircular Indeterminate="true" Size="Size.Medium" />
                <MudText Class="ml-3">正在加载预览...</MudText>
            </div>
        }
        else if (!string.IsNullOrEmpty(errorMessage))
        {
            <MudAlert Severity="Severity.Warning" Class="mb-4">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
                    <div>
                        <MudText Typo="Typo.body1" Class="font-weight-medium">无法预览此文件</MudText>
                        <MudText Typo="Typo.body2">@errorMessage</MudText>
                    </div>
                </div>
            </MudAlert>
            
            <div class="text-center">
                <MudButton Variant="Variant.Outlined" 
                         Color="Color.Primary" 
                         StartIcon="@Icons.Material.Filled.Download"
                         OnClick="@DownloadFile">
                    下载文件查看
                </MudButton>
            </div>
        }
        else
        {
            <!-- 文件信息卡片 -->
            <MudCard Class="mb-4" Elevation="2">
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.InsertDriveFile" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">文件名：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Paper?.FileName</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">上传时间：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Paper?.UploadedDate.ToString("yyyy年MM月dd日 HH:mm")</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Category" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">文件类型：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@GetFileTypeDisplay()</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Storage" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">文件大小：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@GetFileSize()</MudText>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>

            <!-- 预览内容区域 -->
            <MudPaper Class="pa-4" Elevation="1" Style="min-height: 700px; max-height: 800px; overflow: auto;">
                @if (fileExtension == ".pdf")
                {
                    <div style="width: 100%; height: 700px;">
                        <iframe src="@previewUrl"
                                width="100%"
                                height="100%"
                                style="border: none; display: block;"
                                frameborder="0"
                                scrolling="auto">
                        </iframe>
                    </div>
                }
                else if (IsImageFile())
                {
                    <div class="text-center">
                        <img src="@previewUrl" 
                             alt="试卷预览" 
                             style="max-width: 100%; height: auto;" />
                    </div>
                }
                else if (IsTextFile())
                {
                    @if (ShouldUseIframeForText())
                    {
                        <div style="width: 100%; height: 700px;">
                            <iframe src="@previewUrl"
                                    width="100%"
                                    height="100%"
                                    style="border: 1px solid #ddd; display: block;"
                                    frameborder="0"
                                    scrolling="auto"
                                    sandbox="@(fileExtension == ".html" ? "allow-same-origin" : null)">
                            </iframe>
                        </div>
                    }
                    else
                    {
                        <div style="white-space: pre-wrap; font-family: monospace; font-size: 14px;">
                            @textContent
                        </div>
                    }
                }
                else
                {
                    <div class="text-center pa-8">
                        <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Large" Color="Color.Info" Class="mb-4" />
                        <MudText Typo="Typo.h6" Class="mb-2">此文件类型暂不支持在线预览</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Info" Class="mb-4">
                            支持预览的格式：PDF、图片文件、文本文件
                        </MudText>
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 StartIcon="@Icons.Material.Filled.Download"
                                 OnClick="@DownloadFile">
                            下载文件
                        </MudButton>
                    </div>
                }
            </MudPaper>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel" 
                 StartIcon="@Icons.Material.Filled.Close"
                 Color="Color.Default">
            关闭
        </MudButton>
        <MudButton Color="Color.Primary" 
                 OnClick="@DownloadFile" 
                 StartIcon="@Icons.Material.Filled.Download"
                 Variant="Variant.Outlined">
            下载文件
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public ExamPaper Paper { get; set; } = null!;
    
    private bool loading = true;
    private string errorMessage = string.Empty;
    private string previewUrl = string.Empty;
    private string textContent = string.Empty;
    private string fileExtension = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadPreview();
    }

    private async Task LoadPreview()
    {
        try
        {
            loading = true;
            StateHasChanged();

            if (Paper == null || !File.Exists(Paper.FilePath))
            {
                errorMessage = "文件不存在或已被删除";
                return;
            }

            fileExtension = Path.GetExtension(Paper.FileName).ToLower();

            // 根据文件类型处理预览
            if (fileExtension == ".pdf")
            {
                // PDF 文件通过 API 提供预览，添加参数控制显示
                previewUrl = $"/api/ExamFile/preview-paper/{Path.GetFileName(Paper.FilePath)}#view=FitH&toolbar=1&navpanes=1&scrollbar=1";
            }
            else if (IsImageFile())
            {
                // 图片文件
                previewUrl = $"/api/ExamFile/preview-paper/{Path.GetFileName(Paper.FilePath)}";
            }
            else if (IsTextFile())
            {
                if (ShouldUseIframeForText())
                {
                    // 使用iframe预览HTML、XML、JSON、CSS、JS文件
                    previewUrl = $"/api/ExamFile/preview-paper/{Path.GetFileName(Paper.FilePath)}";
                }
                else
                {
                    // 其他文本文件直接读取内容
                    try
                    {
                        textContent = await File.ReadAllTextAsync(Paper.FilePath);
                        if (textContent.Length > 50000) // 限制显示长度
                        {
                            textContent = textContent.Substring(0, 50000) + "\n\n... (文件内容过长，已截断，请下载完整文件查看)";
                        }
                    }
                    catch
                    {
                        errorMessage = "无法读取文件内容";
                    }
                }
            }
            else
            {
                errorMessage = $"不支持预览 {fileExtension} 格式的文件";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"加载预览失败: {ex.Message}";
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private bool IsImageFile()
    {
        var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
        return imageExtensions.Contains(fileExtension);
    }

    private bool IsTextFile()
    {
        var textExtensions = new[] { ".txt", ".csv", ".json", ".xml", ".html", ".css", ".js" };
        return textExtensions.Contains(fileExtension);
    }

    private bool ShouldUseIframeForText()
    {
        var iframeTextExtensions = new[] { ".html", ".xml", ".json", ".css", ".js" };
        return iframeTextExtensions.Contains(fileExtension);
    }

    private string GetFileTypeDisplay()
    {
        return fileExtension.ToUpper() switch
        {
            ".PDF" => "PDF 文档",
            ".DOC" => "Word 文档",
            ".DOCX" => "Word 文档",
            ".PPT" => "PowerPoint 演示文稿",
            ".PPTX" => "PowerPoint 演示文稿",
            ".XLS" => "Excel 表格",
            ".XLSX" => "Excel 表格",
            ".TXT" => "文本文件",
            ".CSV" => "CSV 表格",
            ".JPG" or ".JPEG" => "JPEG 图片",
            ".PNG" => "PNG 图片",
            ".GIF" => "GIF 图片",
            _ => fileExtension.ToUpper() + " 文件"
        };
    }

    private string GetFileSize()
    {
        try
        {
            if (Paper != null && File.Exists(Paper.FilePath))
            {
                var fileInfo = new FileInfo(Paper.FilePath);
                var bytes = fileInfo.Length;
                
                if (bytes < 1024) return $"{bytes} B";
                if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
                return $"{bytes / (1024 * 1024):F1} MB";
            }
        }
        catch {
            // ignored
        }

        return "未知";
    }

    private async Task DownloadFile()
    {
        try
        {
            var url = $"/api/ExamFile/download-paper/{Path.GetFileName(Paper.FilePath)}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add("下载已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
